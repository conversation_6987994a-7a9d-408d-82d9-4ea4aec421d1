from typing import TYPE_CHECKING

import discord

if TYPE_CHECKING:
    from ....cogs.story_cog import StoryCog
    from ...story_master_view import StoryMasterView


class BackToStoryButton(discord.ui.Button):
    """
    A button that returns the user to the main story view from the summary management view.
    """

    def __init__(self, story_cog: "StoryCog", parent_view: "StoryMasterView"):
        super().__init__(
            label="⬅️ 返回故事",
            style=discord.ButtonStyle.secondary,
            custom_id="back_to_story",
            row=4,
        )
        self.story_cog = story_cog
        self.parent_view = parent_view

    async def callback(self, interaction: discord.Interaction):
        """
        Handles the button click event. Returns to the story view.
        """
        # Since parent_view is the original StoryMasterView instance, we can reuse it.
        # We just need to trigger its update logic.
        await self.parent_view._update_page(self.parent_view.current_page, interaction)
