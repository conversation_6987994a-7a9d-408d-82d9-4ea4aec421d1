from typing import TYPE_CHECKING, Optional, Union

import discord

from auxiliary.repositories.story_repository import Story as StoryMaster
from auxiliary.repositories.story_repository import StoryChunkSummary as StoryChunk
from auxiliary.repositories.story_repository import StoryRepository
from auxiliary.views.ui_components.buttons.back_to_story_button import BackToStoryButton
from gacha.views.collection.collection_view.base_pagination import BasePaginationView

if TYPE_CHECKING:
    from bot import ServantBot

    from ..cogs.story_cog import StoryCog
    from .story_master_view import StoryMasterView


class SummaryManagementView(BasePaginationView):
    """
    A view dedicated to managing and displaying story chunk summaries.
    This view handles pagination through summaries and provides a way to return
    to the main story view.
    """

    def __init__(
        self,
        bot: "ServantBot",
        user: Union[discord.User, discord.Member],
        story_cog: "StoryCog",
        story_id: int,
        parent_view: "StoryMasterView",
    ):
        self.bot = bot
        self.user = user
        self.story_cog = story_cog
        self.story_id = story_id
        self.parent_view = parent_view
        self.story_repo: StoryRepository = self.bot.get_cog("StoryCog").story_repo
        self.story_master: Optional[StoryMaster] = None
        self.summaries: list[StoryChunk] = []

        super().__init__(
            bot=bot,
            user_id=user.id,
            current_page=1,
            total_pages=1,  # Will be updated in _initialize_data
            timeout=None,
        )

    async def _initialize_data(self):
        """Fetches the necessary story data and summaries."""
        self.story_master = await self.story_repo.get_story_master(self.story_id)
        if self.story_master:
            self.summaries = await self.story_repo.get_chunk_summaries_for_story(
                self.story_id
            )
            self.total_pages = len(self.summaries) if self.summaries else 1
            if self.btn_jump:
                self.btn_jump.label = f"{self.current_page}/{self.total_pages}"

    async def create_initial_embed(self) -> discord.Embed:
        """Creates the first embed to be displayed."""
        await self._initialize_data()
        self._refresh_button_states()
        return await self._build_embed_for_page(self.current_page)

    async def _build_embed_for_page(self, page: int) -> discord.Embed:
        """Builds the embed for a specific page of summaries."""
        if not self.story_master or not self.summaries:
            return discord.Embed(
                title="錯誤",
                description="無法載入故事摘要。",
                color=discord.Color.red(),
            )

        summary_chunk = self.summaries[page - 1]

        embed = discord.Embed(
            title=f"【{self.story_master.title}】長期記憶總結",
            description=f"以下是故事第 `{summary_chunk.start_turn_number}` 至 `{summary_chunk.end_turn_number}` 回合的內容總結。",
            color=discord.Color.blurple(),
        )
        embed.add_field(name="總結內容", value=summary_chunk.content, inline=False)
        embed.set_footer(text=f"總結 {page}/{self.total_pages}")

        return embed

    async def _update_page(self, page: int, interaction: discord.Interaction):
        """Updates the message with the new page content."""
        # This method is called by the parent class's button callbacks
        embed = await self._build_embed_for_page(page)
        await interaction.response.edit_message(embed=embed, view=self)

    def add_pagination_buttons(self, row: int = 0):
        """Overrides the parent method to add the back button."""
        super().add_pagination_buttons(row=row)
        self.add_item(BackToStoryButton(self.story_cog, self.parent_view))
